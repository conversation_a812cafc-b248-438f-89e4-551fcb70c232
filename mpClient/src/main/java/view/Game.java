package view;

import com.teamdev.jxbrowser.cookie.Cookie;
import com.teamdev.jxbrowser.cookie.CookieStore;
import com.teamdev.jxbrowser.js.JsFunctionCallback;
import com.teamdev.jxbrowser.media.event.AudioStartedPlaying;
import com.teamdev.jxbrowser.navigation.event.LoadFinished;
import com.teamdev.jxbrowser.navigation.event.NavigationFinished;
import com.teamdev.jxbrowser.profile.Profile;
import com.teamdev.jxbrowser.zoom.ZoomLevel;
import env.Global;
import javafx.application.Platform;
import javafx.stage.Stage;
import javafx.stage.WindowEvent;
import mgr.BrowserManager;
import msg.GameMsg;
import net.MyNetworkDelegate;
import org.eclipse.jetty.server.Server;
import tool.Listener;
import tool.PortTest;

import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

public class Game extends Base {
    // 默认宽高
    public static int defaultWidth = 384;
    public static int defaultHeight = 606;

    // 对应的配置id
    private String id;
    private Listener listener;
    // 回消息
    private boolean responseLaunch = false;
    // 配置的名称，启动的时候发送过来
    public String configName = "";

    public Game() {
        super();
        if (this.width == 0 && this.height == 0) {
            this.width = defaultWidth;
            this.height = defaultHeight;
        }
        this.title = "";
        this.debug = true;
        this.url = "http://localhost:5174/";
        // 游戏窗口大小不再被锁定
        this.resizeable = true;
    }

    @Override
    public String getId() {
        return id;
    }

    public Game setId(String id) {
        this.id = id;
        this.proName = "game_" + id;
        return this;
    }

    public Game setUrl(String url) {
        this.url = url;
        return this;
    }

    @Override
    public void start(Stage primaryStage) throws Exception {
        if (Global.remote_http && this.proName.startsWith("game_")) {
            this.url = "game://game/index.html";
        }

        // 获取zoom配置
        String zoom = (String) Global.dashboard.exec("Events.GetZoom('" + this.getId() + "');");
        System.out.println("game init zoom:" + zoom);
        this.zoom = Double.parseDouble(zoom);
        super.start(primaryStage);
    }

    @Override
    public void createBrowser() {
        if (Objects.equals(this.id, "")) {
            System.out.println("game 的配置id未被设置，无法创建浏览器");
            return;
        }
        Profile pro = this.checkAndGetProfile();
        this.browser = pro.newBrowser();
        browser.settings().allowJavaScriptAccessClipboard();
        // 静音模式
        browser.audio().on(AudioStartedPlaying.class, evt -> {
            evt.audio().mute();
        });

        // 开启网络req拦截
        MyNetworkDelegate networkDelegate = new MyNetworkDelegate(pro.network());
        networkDelegate.doDelegate();
    }


    @Override
    public void initEvt() {
        window.putProperty("REQ_JAVA_LOAD_LOGIC", (JsFunctionCallback) this::loadLogic);
        window.putProperty("REQ_JAVA_LOAD_FUNCTION", (JsFunctionCallback) this::loadFunction);
        window.putProperty("REQ_SAFE_LOCK_PWD", (JsFunctionCallback) this::getLockPwd);
        window.putProperty("SET_JAVA_APP_TITTLE", (JsFunctionCallback) this::changeTitle);
        window.putProperty("RELOAD_CALL_BACK", (JsFunctionCallback) this::reloadCallBack);
        window.putProperty("DASHBOARD_EXEC", (JsFunctionCallback) this::dashboardExec);

        window.putProperty("READ_FILE_SYNC", (JsFunctionCallback) this::readFileSync);
        window.putProperty("MAKE_DIR_SYNC", (JsFunctionCallback) this::mkdirSync);
        window.putProperty("WRITE_FILE_SYNC", (JsFunctionCallback) this::writeFileSync);
        window.putProperty("LOAD_GAME_SCRIPT", (JsFunctionCallback) this::loadGameScript);
        window.putProperty("DOWNLOAD_FILE", (JsFunctionCallback) this::downloadFile);
        window.putProperty("ACCESS_FILE", (JsFunctionCallback) this::accessFile);
        window.putProperty("COPY_FILE", (JsFunctionCallback) this::copyFile);
        super.initEvt();
    }

    @Override
    protected void reloadPage() {
        this.execQuickNr("Evt.emit('EVT_RELOAD', `RELOAD_CALL_BACK && RELOAD_CALL_BACK()`)");
    }

    // 提供给game客户端 调用dashboard的方法
    private Object dashboardExec(Object... args) {
        String jsCode = String.valueOf(args[0]);
        if (jsCode.equals("")) {
            return null;
        }
        return Global.dashboard.exec(jsCode);
    }

    private String reloadCallBack(Object... args) {
        if (Global.debug && Global.local_logic) {
            // dev环境下，重新加载logic
            Global.dashboard.checkLocalLogic();
        }
        this.execQuickNr("location.href = \"" + this.url + "\"");
        this.clearFrame();
        return "";
    }

    // 注入代码
    private String loadLogic(Object... args) {
        String code = Global.dashboard.getCode();
        this.exec(code);
        this.execQuickNr("loadGameScript();");
        this.sendMsg(GameMsg.LOGIC_INJECT_DONE);
        return "";
    }

    private String loadFunction(Object... args) {
        return Global.dashboard.getFunction();
    }

    // 获取安全锁的密码
    private String getLockPwd(Object... args) {
        Object exec = Global.dashboard.exec("Events.__get_safe_password(" + this.id + ")");
        return (String) exec;
    }

    // 修改窗口标题
    private String changeTitle(Object... args) {
        Platform.runLater(() -> {
            String name = " [ " + args[0] + " ] ";
            if (!this.configName.equals("")) {
                name = this.configName + "-" + name;
            }
            this.stage.titleProperty().setValue(name);
            if (this.debugWindow != null)
                this.debugWindow.stage.titleProperty().setValue(" [ " + args[0] + " ] ");
        });
        return "";
    }

    // evt 派发
    public void sendMsg(GameMsg code) {
        this.execQuickNr("Events.onMsg(" + code.ordinal() + ")");
    }

    @Override
    public void onLoadFinished(LoadFinished loadFinished) {
        super.onLoadFinished(loadFinished);
        // 放入id参数
        window.putProperty("GAME_ID", this.id);
        window.putProperty("GAME_CONFIG_NAME", this.configName);
        // 进行一次 zoom 事件
        this.execQuickNr("typeof ON_ZOOM_EVT != 'undefined' && ON_ZOOM_EVT(" + this.zoom + ")");
        this.browser.zoom().level(ZoomLevel.P_100);
        if (!this.responseLaunch) {
            this.notifyLaunchSuccess();
            this.responseLaunch = true;
        }
        this.execQuickNr("(async function(){const sid=setInterval(()=>{if(window.REQ_JAVA_LOAD_LOGIC!=null){window.REQ_JAVA_LOAD_LOGIC();clearInterval(sid)}},100)})();");
    }

    public void onKeyEvent(String key) {
        synchronized (this) {
            super.onKeyEvent(key);
        }
    }

    @Deprecated
    protected void openMapTool() {
        this.execQuickNr("");
    }

    @Deprecated
    protected void backToCity() {
        this.execQuickNr("");
    }

    @Override
    public void onNavigationFinished(NavigationFinished navigationFinished) {
        super.onNavigationFinished(navigationFinished);
    }

    @Override
    public void onClose(WindowEvent event) {
        try {
            if (this.debugWindow != null) {
                this.debugWindow.onClose(event);
            }
            if (this.listener != null && this.listener.isAlive()) {
                this.listener.run = false;
            }
            if (event != null) {
                this.notifyStop();
            }
            this.setPositionOnClose();
            this.setSceneSizeOnClose();
            // 重置zoom率
            this.browser.zoom().reset();
            this.browser.close();
            this.stage.close();
            Global.dashboard.games.remove(this.id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知启动成功了
     */
    public void notifyLaunchSuccess() {
        Global.dashboard.execQuickNr("Events._launch_result_" + id + "(0)");
    }

    /**
     * 通知停止game
     */
    public void notifyStop() {
        Global.dashboard.execQuickNr("Events._stop_result_" + id + "(1)");
    }


    public void setZoom(double z) {
        if (this.zoom == z) return;
        this.zoom = z;
//        this.browser.zoom().level(ZoomLevel.of(z));
        this.execQuickNr(" typeof ON_ZOOM_EVT != 'undefined' && ON_ZOOM_EVT(" + z + ")");
    }

    public Object readFileSync(Object... args) {
        try {
            String url = (String) args[0];
            String encoding = (String) args[1];
            String property = System.getProperty("user.dir");
            System.out.println("readFileSync:" + url + ", " + encoding);

            if (url.startsWith("/")) {
                url = url.substring(1);
            }
            Path path = Paths.get(property);
            if (!url.contains("src/test/remote/")) {
                path = path.resolve("src/test/remote/");
            }
            path = path.resolve(url);
            byte[] buffer = Files.readAllBytes(path.toAbsolutePath());
            if (Objects.equals(encoding, "utf8")) {
                return new String(buffer, StandardCharsets.UTF_8);
            }
            return buffer;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public Object mkdirSync(Object... args) {
        try {
            String url = (String) args[0];
            boolean recursive = (boolean) args[1];
            String property = System.getProperty("user.dir");
            File file = Paths.get(property).resolve(url).toFile();
            if (!file.exists()) {
                if (recursive) {
                    return file.mkdirs();
                } else {
                    return file.mkdir();
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Object writeFileSync(Object... args) {
        try {
            String filePath = args[0].toString();
            String data = args[1].toString();
            String encoding = args[2].toString();
            // 处理不同类型的数据
            byte[] bytesToWrite;
            if ("base64".equals(encoding)) {
                // Base64解码
                bytesToWrite = java.util.Base64.getDecoder().decode(data);
            } else {
                // UTF-8或其他文本编码
                bytesToWrite = data.getBytes(java.nio.charset.StandardCharsets.UTF_8);
            }

            Path to = Paths.get(System.getProperty("user.dir")).resolve(filePath);
            File file = to.toFile();
            java.io.File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            // 写入文件
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(file)) {
                fos.write(bytesToWrite);
                fos.flush();
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Object loadGameScript(Object... args) {
        String val = this.readFileSync(args).toString();
        if (val != null) {
            this.exec(val);
        }
        return null;
    }

    public Object downloadFile(Object... args) {
        try {
            String url = args[0].toString();
            String property = System.getProperty("user.dir");
            Path basePath = Paths.get(property).resolve("src/test/remote/");

            // 从URL中提取路径部分（去掉域名）
            String relativePath = extractPathFromUrl(url);
            if (relativePath == null || relativePath.isEmpty()) {
                System.err.println("无法从URL中提取有效路径: " + url);
                return "";
            }

            // 构建完整的本地文件路径
            Path targetPath = basePath.resolve(relativePath);
            File targetFile = targetPath.toFile();

            String link = targetFile.getAbsolutePath().replaceAll(System.getProperty("user.dir"), "");
            // 如果文件已存在，直接返回成功
            if (targetFile.exists()) {
                System.out.println("文件已存在，跳过下载: " + targetPath);
                return link;
            }

            // 确保父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 执行下载
            System.out.println("开始下载文件: " + url + " -> " + targetPath);
            if (downloadFileFromUrl(url, targetFile)) {
                return link;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private String extractPathFromUrl(String url) {
        try {
            // 移除协议部分
            if (url.startsWith("http://") || url.startsWith("https://")) {
                int protocolEnd = url.indexOf("://") + 3;
                String withoutProtocol = url.substring(protocolEnd);

                // 找到第一个斜杠，后面就是路径部分
                int firstSlash = withoutProtocol.indexOf('/');
                if (firstSlash != -1) {
                    return withoutProtocol.substring(firstSlash + 1);
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private boolean downloadFileFromUrl(String url, File targetFile) {
        try {
            java.net.URL fileUrl = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) fileUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            connection.connect();

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                try (InputStream inputStream = connection.getInputStream();
                     FileOutputStream outputStream = new FileOutputStream(targetFile)) {

                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }
                System.out.println("文件下载成功: " + targetFile.getAbsolutePath());

                return true;
            } else {
                System.err.println("下载失败，HTTP状态码: " + responseCode);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    public Object accessFile(Object... args) {
        String url = args[0].toString();
        String property = System.getProperty("user.dir");
        Path path = Paths.get(property);
        if (url.startsWith("/")) {
            url = url.substring(1);
        }
        if (!url.contains("src/test/remote/")) {
            path = path.resolve("src/test/remote/");
        }
        path = path.resolve(url);
        return path.toFile().exists();
    }


    public Object copyFile(Object... args) {
        String srcPath = args[0].toString();
        String destPath = args[1].toString();
        String property = System.getProperty("user.dir");
        if (srcPath.startsWith("/")) {
            srcPath = srcPath.substring(1);
        }

        if (destPath.startsWith("/")) {
            destPath = destPath.substring(1);
        }
        try {
            Path source = Paths.get(property).resolve(srcPath);
            Path destination = Paths.get(property).resolve(destPath);
            Files.copy(source, destination);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
